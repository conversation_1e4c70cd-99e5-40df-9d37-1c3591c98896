import base64
import hashlib
import hmac
import logging
from datetime import datetime
from time import mktime
from urllib.parse import urlencode
from wsgiref.handlers import format_date_time

from app.core.config import settings

logger = logging.getLogger(__name__)


def get_speech_to_text_ws_url() -> str:
    """
    获取语音听写 WebSocket URL

    根据讯飞开放平台的规则生成带鉴权参数的 WebSocket URL

    Returns:
        str: WebSocket URL，如 wss://ws-api.xfyun.cn/v2/iat?authorization=...&date=...&host=...
    """
    # 从配置中获取语音听写相关参数
    api_key = settings.STT_API_KEY
    api_secret = settings.STT_API_SECRET
    host = settings.STT_API_HOST

    # 检查必要的配置是否存在
    if not api_key or not api_secret:
        logger.error("STT_API_KEY or STT_API_SECRET not configured")
        raise ValueError("Speech to Text service not properly configured")

    # 基础 URL
    url = f'wss://{host}/v2/iat'

    # 生成 RFC1123 格式的时间戳
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))

    # 拼接字符串
    signature_origin = f"host: {host}\n"
    signature_origin += f"date: {date}\n"
    signature_origin += "GET /v2/iat HTTP/1.1"

    # 进行 hmac-sha256 签名
    signature_sha = hmac.new(
        api_secret.encode('utf-8'),
        signature_origin.encode('utf-8'),
        digestmod=hashlib.sha256
    ).digest()

    # 进行 base64 编码
    signature_sha_base64 = base64.b64encode(signature_sha).decode('utf-8')

    # 构造 authorization
    authorization_origin = f'api_key="{api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha_base64}"'
    authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode('utf-8')

    # 构造 URL 参数
    v = {
        "authorization": authorization,
        "date": date,
        "host": host
    }

    # 拼接最终的 WebSocket URL
    ws_url = f"{url}?{urlencode(v)}"

    logger.debug(f"Generated WebSocket URL: {ws_url}")

    return ws_url