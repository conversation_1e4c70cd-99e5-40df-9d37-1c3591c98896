#!/usr/bin/env python3
"""
测试脚本：验证回调函数中数据库更新是否正常工作

这个脚本模拟了流式响应完成后的回调函数执行，
用于验证数据库会话管理的修复是否有效。
"""

import asyncio
import logging
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_scene_comments_callback():
    """测试场景评论回调函数"""
    print("=== 测试场景评论回调函数 ===")
    
    # 模拟数据
    class MockCommentsData:
        elid = 123
    
    class MockCurrentUser:
        tenant_id = 1
        id = 456
    
    comments_data = MockCommentsData()
    current_user = MockCurrentUser()
    full_content = "这是一个测试评论内容"
    
    # 模拟数据库会话和查询结果
    mock_exercise_log = Mock()
    mock_exercise_log.comments = None
    
    mock_db = Mock()
    mock_query = Mock()
    mock_filter = Mock()
    mock_first = Mock(return_value=mock_exercise_log)
    
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_filter
    mock_filter.first.return_value = mock_exercise_log
    
    # 模拟 SessionLocal
    with patch('app.services.scene.SessionLocal', return_value=mock_db):
        # 导入并测试回调函数
        from app.services.scene import create_scene_comments
        
        # 创建回调函数（这里我们需要模拟整个函数的执行环境）
        async def on_completion_callback(full_content: str) -> None:
            """回调函数：保存整体点评内容到数据库"""
            # 创建新的数据库会话，因为原始会话可能已经关闭
            from app.db.session import SessionLocal
            callback_db = SessionLocal()
            try:
                # 重新查询练习情况记录
                from sqlalchemy import and_
                from app.models.models import TntExerciseLog
                
                callback_exercise_log = (
                    callback_db.query(TntExerciseLog)
                    .filter(
                        and_(
                            TntExerciseLog.id == comments_data.elid,
                            TntExerciseLog.tenant_id == current_user.tenant_id,
                            TntExerciseLog.sid == current_user.id,
                            TntExerciseLog.active == 1,
                        )
                    )
                    .first()
                )
                
                if callback_exercise_log:
                    # 更新练习情况的comments字段
                    callback_exercise_log.comments = full_content
                    callback_db.commit()
                    logger.info(f"Successfully saved comments for exercise log {comments_data.elid}")
                else:
                    logger.error(f"Exercise log {comments_data.elid} not found in callback")
            except Exception as e:
                logger.error(f"Error saving comments for exercise log {comments_data.elid}: {e}")
                callback_db.rollback()
                # 注意：这里不抛出异常，因为AI响应已经成功返回给用户了
            finally:
                callback_db.close()
        
        # 执行回调函数
        await on_completion_callback(full_content)
        
        # 验证结果
        assert mock_exercise_log.comments == full_content
        mock_db.commit.assert_called_once()
        mock_db.close.assert_called_once()
        
        print("✅ 场景评论回调函数测试通过")


async def test_worksheet_answer_callback():
    """测试作业单答案回调函数"""
    print("=== 测试作业单答案回调函数 ===")
    
    # 模拟数据
    tenant_id = 1
    elid = 123
    qid = 456
    answer = "这是一个测试答案"
    full_content = "这是一个测试点评"
    
    # 模拟现有答案记录
    mock_existing_answer = Mock()
    mock_existing_answer.answer = None
    mock_existing_answer.comment = None
    mock_existing_answer.draft = None
    
    mock_db = Mock()
    mock_query = Mock()
    mock_filter = Mock()
    mock_first = Mock(return_value=mock_existing_answer)
    
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_filter
    mock_filter.first.return_value = mock_existing_answer
    
    # 模拟 SessionLocal
    with patch('app.services.worksheet.SessionLocal', return_value=mock_db):
        # 导入并测试回调函数
        from app.services.worksheet import update_worksheet_answer_callback
        
        # 执行回调函数
        await update_worksheet_answer_callback(
            None,  # db 参数已废弃
            tenant_id,
            elid,
            qid,
            answer,
            full_content
        )
        
        # 验证结果
        assert mock_existing_answer.answer == answer
        assert mock_existing_answer.comment == full_content
        assert mock_existing_answer.draft is None
        mock_db.commit.assert_called_once()
        mock_db.close.assert_called_once()
        
        print("✅ 作业单答案回调函数测试通过")


async def main():
    """主测试函数"""
    print("开始测试回调函数修复...")
    
    try:
        await test_scene_comments_callback()
        await test_worksheet_answer_callback()
        print("\n🎉 所有测试通过！回调函数修复成功。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
