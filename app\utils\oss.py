import os
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import oss2

from app.core.config import settings


def get_oss_url(object_name: str, expire_seconds: int = 3600) -> str:
    """
    获取OSS文件的签名URL

    Args:
        object_name: OSS中的文件路径
        expire_seconds: 签名有效期，单位秒，默认1小时

    Returns:
        签名后的URL
    """
    # 如果object_name为空或None，直接返回空字符串
    if not object_name:
        return ""

    # 如果object_name已经是完整URL，直接返回
    if object_name.startswith("http"):
        return object_name

    # 初始化OSS客户端
    auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
    bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET)

    # 生成带签名的URL
    url = bucket.sign_url("GET", object_name, expire_seconds, slash_safe=True)
    return url


def generate_oss_signature(
    file_path: str, content_type: Optional[str] = None, expire_seconds: int = 300
) -> Dict[str, Any]:
    """
    生成OSS直传签名

    Args:
        file_path: 文件路径，相对于bucket根目录
        content_type: 文件内容类型，如image/jpeg
        expire_seconds: 签名有效期，单位秒

    Returns:
        包含签名信息的字典
    """
    # 初始化OSS客户端
    auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
    bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET)

    # 计算过期时间
    expire_time = int((datetime.now() + timedelta(seconds=expire_seconds)).timestamp())

    # 生成签名URL
    params = {"expires": expire_seconds}

    headers = {}
    if content_type:
        headers["Content-Type"] = content_type

    # 生成PUT签名URL
    url = bucket.sign_url("PUT", file_path, expire_seconds, headers=headers)

    return {
        "upload_url": url,
        "file_path": file_path,
        "file_url": f"{settings.OSS_BASE_URL}/{file_path}",
        "headers": headers,
        "expires": expire_time,
    }


def generate_oss_image_upload_url(
    upload_path: str, file_extension: str
) -> Dict[str, Any]:
    """
    生成OSS图片上传的签名URL

    Args:
        file_extension: 文件扩展名，如jpg, png等

    Returns:
        包含签名信息的字典
    """
    # 生成唯一文件名
    file_name = f"{uuid.uuid4()}.{file_extension}"

    # 构建文件路径，放在avatar目录下
    file_path = f"{upload_path}/{file_name}"

    # 根据扩展名确定内容类型
    content_type_map = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "gif": "image/gif",
        "webp": "image/webp",
    }

    content_type = content_type_map.get(
        file_extension.lower(), "application/octet-stream"
    )

    # 生成签名
    return generate_oss_signature(file_path, content_type)


def delete_oss_file(object_name: str) -> Dict[str, Any]:
    """
    删除OSS文件

    Args:
        object_name: OSS中的文件相对路径

    Returns:
        删除结果字典，包含成功状态和消息
    """
    # 检查文件路径是否为空
    if not object_name:
        return {
            "success": False,
            "message": "文件路径不能为空"
        }

    # 只接受相对路径，不接受完整URL
    if object_name.startswith("http"):
        return {
            "success": False,
            "message": "请使用相对路径，不支持完整URL"
        }

    try:
        # 初始化OSS客户端
        auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET)

        # 删除文件
        bucket.delete_object(object_name)

        return {
            "success": True,
            "message": "文件删除成功",
            "file_path": object_name
        }

    except oss2.exceptions.NoSuchKey:
        return {
            "success": False,
            "message": "文件不存在"
        }
    except oss2.exceptions.AccessDenied:
        return {
            "success": False,
            "message": "访问被拒绝，请检查OSS权限配置"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"删除文件失败: {str(e)}"
        }


def get_file_extension(filename: str) -> str:
    """
    从文件名中获取扩展名

    Args:
        filename: 文件名

    Returns:
        文件扩展名，不包含点号
    """
    _, ext = os.path.splitext(filename)
    if ext.startswith("."):
        ext = ext[1:]
    return ext


def extract_object_name_from_url(oss_url: str) -> str:
    """
    从OSS signed URL中提取文件的相对路径

    Args:
        oss_url: OSS的签名URL

    Returns:
        文件在OSS中的相对路径
        
    Raises:
        ValueError: 如果URL格式不正确或不是有效的OSS URL
    """
    if not oss_url:
        raise ValueError("URL不能为空")
        
    if not oss_url.startswith("http"):
        raise ValueError("请提供完整的OSS URL")
    
    try:
        # 解析URL
        parsed_url = urlparse(oss_url)
        
        # 检查是否是OSS域名
        if settings.OSS_BUCKET not in parsed_url.netloc:
            raise ValueError("不是有效的OSS URL")
        
        # 提取路径，去掉开头的斜杠
        object_name = parsed_url.path.lstrip('/')
        
        if not object_name:
            raise ValueError("无法从URL中提取文件路径")
            
        return object_name
        
    except Exception as e:
        raise ValueError(f"解析OSS URL失败: {str(e)}")
