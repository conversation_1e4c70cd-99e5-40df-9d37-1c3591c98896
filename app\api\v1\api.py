from fastapi import APIRouter

from app.api.v1.endpoints import auth, system, clazz, exercise, worksheet, scene, misc, tts

api_router = APIRouter()

# System endpoints
api_router.include_router(system.router, prefix="/sys", tags=["sys"])

# Auth endpoints
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

# Class endpoints
api_router.include_router(clazz.router, prefix="/class", tags=["class"])

# Exercise endpoints
api_router.include_router(exercise.router, prefix="/exercise", tags=["exercise"])

# Worksheet endpoints
api_router.include_router(worksheet.router, prefix="/worksheet", tags=["worksheet"])

# Scene endpoints
api_router.include_router(scene.router, prefix="/scene", tags=["scene"])

# Misc endpoints
api_router.include_router(misc.router, prefix="/misc", tags=["misc"])

# TTS endpoints
api_router.include_router(tts.router, prefix="/tts", tags=["tts"])