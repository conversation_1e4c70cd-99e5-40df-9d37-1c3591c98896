from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntStudent
from app.schemas.auth import (
    LoginRequest,
    LoginResponse,
    LogoutResponse,
    PasswordUpdateRequest,
    ProfileResponse,
)
from app.schemas.base import MessageResponse
from app.services.auth import (
    get_current_user,
    get_user_profile,
    login,
    logout,
    update_user_password_service,
)

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
def login_api(request: LoginRequest, db: Session = Depends(get_db)):
    """
    用户登录

    ## 功能描述
    用户使用用户名和密码进行身份验证登录。

    ## 请求参数
    - **request** (LoginRequest): 登录请求信息，请求体
        - username: 用户名
        - password: 密码

    ## 响应
    - **200**: 登录成功
        - 返回类型: LoginResponse
        - 包含访问令牌和令牌类型
            - token: JWT访问令牌
            - token_type: 令牌类型，固定为"bearer"
            - uid: 用户ID
            - username: 用户名
            - name: 姓名
            - tenant_id: 租户ID

    ## 权限要求
    - 无需身份验证（这是登录接口）

    ## 错误处理
    - **400**: 登录失败
        - 用户名或密码错误时返回此错误
    """
    member, token, error = login(db, request.username, request.password)
    if error:
        raise HTTPException(status_code=400, detail=error)
    return {
        "token": token,
        "token_type": "bearer",
        "uid": member.uid if member else None,
        "username": member.user.username if member else None,
        "name": member.name if member else None,
        "tenant_id": member.tenant_id if member else None,
    }


@router.post("/logout", response_model=LogoutResponse)
def logout_api(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    """
    用户登出

    ## 功能描述
    用户注销登录，使当前访问令牌失效。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 登出成功
        - 返回类型: LogoutResponse
        - 包含登出成功的消息

    ## 权限要求
    - 需要有效的用户身份令牌

    ## 错误处理
    - **401**: 未授权访问，当令牌无效或过期时返回此错误
    """
    logout(db, current_user.uid)
    return {"message": "登出成功"}


@router.get("/profile", response_model=ProfileResponse)
def get_profile_api(
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取用户个人信息

    ## 功能描述
    获取当前登录用户的个人资料信息。

    ## 请求参数
    - 无需额外参数，通过Authorization header传递Bearer token

    ## 响应
    - **200**: 成功返回个人信息
        - 返回类型: UserProfile
        - 包含用户的个人资料信息
            - uid: 用户ID
            - username: 用户名
            - name: 姓名
            - tenant_id: 租户ID
            - gender: 性别

    ## 权限要求
    - 需要有效的用户身份令牌

    ## 错误处理
    - **404**: 用户信息不存在
        - 当用户记录在数据库中不存在时返回此错误
    """
    member = get_user_profile(db, current_user.id)
    if not member:
        raise HTTPException(status_code=404, detail="User not found")

    # 构造响应对象，包含用户信息
    return ProfileResponse(
        uid=member.uid,
        username=member.user.username,
        name=member.name,
        tenant_id=member.tenant_id,
        gender=member.gender,
    )


@router.put("/password", response_model=MessageResponse)
def update_password_api(
    password_update: PasswordUpdateRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    更新用户密码

    ## 功能描述
    允许用户更新自己的登录密码。

    ## 请求参数
    - **password_update** (UserPasswordUpdate): 密码更新信息，请求体
        - old_password: 当前密码
        - new_password: 新密码

    ## 响应
    - **200**: 密码更新成功
        - 返回成功消息

    ## 权限要求
    - 需要有效的用户身份令牌

    ## 错误处理
    - **400**: 密码更新失败
        - 当前密码错误或用户不存在时返回此错误
    """
    if not update_user_password_service(db, current_user.id, password_update):
        raise HTTPException(
            status_code=400, detail="Incorrect password or user not found"
        )
    return {"message": "密码更新成功"}
