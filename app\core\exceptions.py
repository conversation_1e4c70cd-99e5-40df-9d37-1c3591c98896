"""
自定义异常类

提供业务逻辑相关的异常定义，用于更精确的错误处理和状态码映射。
"""

from typing import Optional


class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class ResourceNotFoundException(BusinessException):
    """资源不存在异常"""
    
    def __init__(self, resource_name: str, resource_id: Optional[str] = None):
        if resource_id:
            message = f"{resource_name}(ID: {resource_id})不存在"
        else:
            message = f"{resource_name}不存在"
        super().__init__(message, "RESOURCE_NOT_FOUND")


class InvalidStatusException(BusinessException):
    """状态无效异常"""
    
    def __init__(self, resource_name: str, current_status: str, expected_status: str):
        message = f"{resource_name}状态不对无法处理，当前状态: {current_status}，期望状态: {expected_status}"
        super().__init__(message, "INVALID_STATUS")


class PermissionDeniedException(BusinessException):
    """权限不足异常"""
    
    def __init__(self, action: str, resource: str):
        message = f"无权限执行操作: {action}，资源: {resource}"
        super().__init__(message, "PERMISSION_DENIED")


class ExternalServiceException(BusinessException):
    """外部服务异常"""
    
    def __init__(self, service_name: str, error_detail: Optional[str] = None):
        if error_detail:
            message = f"{service_name}服务调用失败: {error_detail}"
        else:
            message = f"{service_name}服务调用失败"
        super().__init__(message, "EXTERNAL_SERVICE_ERROR")


class ValidationException(BusinessException):
    """数据验证异常"""
    
    def __init__(self, field_name: str, validation_error: str):
        message = f"数据验证失败，字段: {field_name}，错误: {validation_error}"
        super().__init__(message, "VALIDATION_ERROR")


class DatabaseException(BusinessException):
    """数据库操作异常"""
    
    def __init__(self, operation: str, error_detail: Optional[str] = None):
        if error_detail:
            message = f"数据库操作失败: {operation}，详情: {error_detail}"
        else:
            message = f"数据库操作失败: {operation}"
        super().__init__(message, "DATABASE_ERROR")


# 场景相关的具体异常
class SpeechJobNotFoundException(ResourceNotFoundException):
    """发言任务不存在异常"""
    
    def __init__(self, job_id: str):
        super().__init__("任务", job_id)


class SpeechJobInvalidStatusException(InvalidStatusException):
    """发言任务状态无效异常"""
    
    def __init__(self, current_status: int):
        status_map = {0: "未开始", 1: "进行中", 2: "已完成"}
        current_status_text = status_map.get(current_status, f"未知状态({current_status})")
        super().__init__("任务", current_status_text, "未开始(0)")


class AIServiceException(ExternalServiceException):
    """AI服务异常"""
    
    def __init__(self, error_detail: Optional[str] = None):
        super().__init__("AI", error_detail)


class BotConfigNotFoundException(ResourceNotFoundException):
    """机器人配置不存在异常"""
    
    def __init__(self, bot_key: str):
        super().__init__("机器人配置", bot_key)