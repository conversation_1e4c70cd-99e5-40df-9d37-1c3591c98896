# 流式响应回调函数数据库更新问题修复

## 问题描述

在调用 `get_fastgpt_stream_response` 函数后，前端能够正常接收流式内容，回调函数也能执行，但是数据库中的 `comments` 字段没有更新。

## 问题分析

### 根本原因

问题出现在数据库会话的生命周期管理上：

1. **FastAPI 依赖注入机制**：`get_db()` 函数使用 `yield` 模式管理数据库会话
2. **会话自动关闭**：当 HTTP 请求结束时，FastAPI 会自动关闭数据库会话
3. **回调执行时机**：`on_completion` 回调函数在流式响应完成后的 `finally` 块中执行
4. **会话已关闭**：回调函数执行时，原始的数据库会话可能已经被关闭

### 具体流程

```
1. HTTP 请求开始 → 创建数据库会话
2. 调用 get_fastgpt_stream_response → 返回 StreamingResponse
3. HTTP 响应开始流式传输 → FastAPI 准备关闭会话
4. 流式传输完成 → 会话被关闭
5. finally 块执行 → 回调函数尝试使用已关闭的会话 ❌
```

## 解决方案

### 修复策略

在回调函数中创建新的数据库会话，而不是使用原始的会话：

1. **创建新会话**：使用 `SessionLocal()` 创建独立的数据库会话
2. **重新查询数据**：在新会话中重新查询需要更新的记录
3. **执行更新操作**：更新数据并提交事务
4. **正确关闭会话**：在 `finally` 块中关闭新创建的会话

### 修复的文件

#### 1. app/services/scene.py

修复了 `create_scene_comments` 函数中的回调函数：

```python
async def on_completion_callback(full_content: str) -> None:
    """回调函数：保存整体点评内容到数据库"""
    # 创建新的数据库会话，因为原始会话可能已经关闭
    from app.db.session import SessionLocal
    callback_db = SessionLocal()
    try:
        # 重新查询练习情况记录
        callback_exercise_log = (
            callback_db.query(TntExerciseLog)
            .filter(...)
            .first()
        )
        
        if callback_exercise_log:
            callback_exercise_log.comments = full_content
            callback_db.commit()
            logger.info(f"Successfully saved comments for exercise log {comments_data.elid}")
    except Exception as e:
        logger.error(f"Error saving comments: {e}")
        callback_db.rollback()
    finally:
        callback_db.close()
```

#### 2. app/services/worksheet.py

修复了 `update_worksheet_answer_callback` 函数：

```python
async def update_worksheet_answer_callback(
    db: Session, tenant_id: int, elid: int, qid: int, answer: str, full_content: str
) -> None:
    # 创建新的数据库会话，因为原始会话可能已经关闭
    from app.db.session import SessionLocal
    callback_db = SessionLocal()
    
    try:
        # 查找现有记录
        existing_answer = (
            callback_db.query(TntWorksheetAnswer)
            .filter(...)
            .first()
        )
        
        if existing_answer:
            # 更新现有记录
            existing_answer.answer = answer
            existing_answer.comment = full_content
        else:
            # 创建新记录
            new_answer = TntWorksheetAnswer(...)
            callback_db.add(new_answer)
        
        callback_db.commit()
        logger.info(f"Successfully saved worksheet answer")
    except Exception as e:
        logger.error(f"Error saving worksheet answer: {e}")
        callback_db.rollback()
    finally:
        callback_db.close()
```

## 修复效果

### 修复前
- ❌ 流式响应正常，但数据库不更新
- ❌ 回调函数执行但操作失败
- ❌ 可能出现数据库连接错误

### 修复后
- ✅ 流式响应正常
- ✅ 数据库正确更新
- ✅ 回调函数正常执行
- ✅ 完整的错误处理和日志记录

## 测试验证

创建了测试脚本 `test_callback_fix.py` 来验证修复效果：

```bash
python test_callback_fix.py
```

## 注意事项

1. **性能考虑**：每个回调函数都会创建新的数据库连接，但这是必要的
2. **事务安全**：每个回调函数都有独立的事务，确保数据一致性
3. **错误处理**：回调函数中的错误不会影响流式响应的返回
4. **日志记录**：添加了详细的日志记录，便于问题排查

## 相关接口

受此修复影响的接口：

1. **POST /api/v1/scene/comments** - 创建整体点评
2. **POST /api/v1/worksheet/question/comment** - 获取问题点评

这些接口现在都能正确保存AI生成的内容到数据库中。
