from datetime import datetime
from typing import Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.models.models import (
    TntClassExercise,
    TntClassStudent,
    TntExerciseLog,
    TntQuestion,
    TntStudent,
    TntWorksheet,
    TntWorksheetAsm,
)
from app.schemas.exercise import (
    ExerciseLogCreateRequest,
    ExerciseLogCreateResponse,
)


def get_existing_exercise_log(
    db: Session, request: ExerciseLogCreateRequest, current_user: TntStudent
) -> Optional[TntExerciseLog]:
    """
    查询是否已存在满足条件的练习记录

    Args:
        db: 数据库会话
        request: 创建练习记录请求
        current_user: 当前用户

    Returns:
        已存在的练习记录或None
    """
    return (
        db.query(TntExerciseLog)
        .filter(
            TntExerciseLog.tenant_id == current_user.tenant_id,
            TntExerciseLog.cid == request.cid,
            TntExerciseLog.sid == current_user.id,
            TntExerciseLog.eid == request.eid,
            TntExerciseLog.active == 1,
        )
        .first()
    )


def validate_exercise_creation(
    db: Session, request: ExerciseLogCreateRequest, current_user: TntStudent
) -> bool:
    """
    验证创建练习记录的合法性
    检查tnt_class_exercise中cid, eid, tenant_id 和 tnt_class_student中 cid, sid, tenant_id 都存在

    Args:
        db: 数据库会话
        request: 创建练习记录请求
        current_user: 当前用户

    Returns:
        验证是否通过
    """
    # 一次查询验证班级练习关系和班级学员关系是否都存在
    result = (
        db.query(
            TntClassExercise.id.label("class_exercise_id"),
            TntClassStudent.id.label("class_student_id"),
        )
        .select_from(TntClassExercise)
        .join(
            TntClassStudent,
            and_(
                TntClassExercise.cid == TntClassStudent.cid,
                TntClassExercise.tenant_id == TntClassStudent.tenant_id,
            ),
        )
        .filter(
            TntClassExercise.cid == request.cid,
            TntClassExercise.eid == request.eid,
            TntClassExercise.tenant_id == current_user.tenant_id,
            TntClassExercise.active == 1,
            TntClassStudent.sid == current_user.id,
            TntClassStudent.tenant_id == current_user.tenant_id,
            TntClassStudent.active == 1,
        )
        .first()
    )

    return result is not None


def create_exercise_log(
    db: Session, request: ExerciseLogCreateRequest, current_user: TntStudent
) -> ExerciseLogCreateResponse:
    """
    创建新的练习记录

    Args:
        db: 数据库会话
        request: 创建练习记录请求
        current_user: 当前用户

    Returns:
        练习记录响应

    Raises:
        Exception: 创建失败时抛出异常
    """
    try:
        # 创建新的练习记录
        exercise_log = TntExerciseLog(
            tenant_id=current_user.tenant_id,
            cid=request.cid,
            sid=current_user.id,
            eid=request.eid,
            btime=datetime.now(),
            status=1,  # 状态设置为练习中
        )

        db.add(exercise_log)
        db.commit()
        db.refresh(exercise_log)

        return ExerciseLogCreateResponse(id=exercise_log.id, btime=exercise_log.btime)

    except Exception as e:
        db.rollback()
        raise e


def validate_exercise_log_update(
    db: Session, log_id: int, current_user: TntStudent
) -> bool:
    """
    验证更新练习记录的合法性
    检查练习记录是否存在，以及对应的练习是否有相关问题

    Args:
        db: 数据库会话
        log_id: 练习记录ID
        current_user: 当前用户

    Returns:
        验证是否通过
    """
    # 验证练习记录存在且属于当前用户，同时检查是否有相关问题
    result = (
        db.query(
            TntExerciseLog.id.label("exercise_log_id"),
            TntQuestion.id.label("question_id"),
        )
        .select_from(TntExerciseLog)
        .join(
            TntWorksheet,
            and_(
                TntWorksheet.eid == TntExerciseLog.eid,
                TntWorksheet.tenant_id == TntExerciseLog.tenant_id,
            ),
        )
        .join(
            TntWorksheetAsm,
            and_(
                TntWorksheetAsm.wid == TntWorksheet.id,
                TntWorksheetAsm.tenant_id == TntWorksheet.tenant_id,
            ),
        )
        .join(
            TntQuestion,
            and_(
                TntQuestion.id == TntWorksheetAsm.qid,
                TntQuestion.tenant_id == TntWorksheetAsm.tenant_id,
                TntQuestion.active == 1,
            ),
        )
        .filter(
            TntExerciseLog.id == log_id,
            TntExerciseLog.sid == current_user.id,
            TntExerciseLog.tenant_id == current_user.tenant_id,
            TntExerciseLog.active == 1,
        )
        .first()
    )

    return result is not None


def update_exercise_log_status(
    db: Session, log_id: int, status: int, current_user: TntStudent
) -> bool:
    """
    更新练习记录

    Args:
        db: 数据库会话
        log_id: 练习记录ID
        status: 更新练习状态
        current_user: 当前用户

    Returns:
        更新是否成功

    Raises:
        Exception: 更新失败时抛出异常
    """
    try:
        # 查找练习记录，确保只能更新属于当前用户的记录
        exercise_log = (
            db.query(TntExerciseLog)
            .filter(
                TntExerciseLog.id == log_id,
                TntExerciseLog.sid == current_user.id,
                TntExerciseLog.tenant_id == current_user.tenant_id,
                TntExerciseLog.active == 1,
            )
            .first()
        )

        if not exercise_log:
            return False

        # 0：待练习；1：练习中；2：已提交
        if status == 0:
            exercise_log.status = 0
            exercise_log.btime = datetime.now()
            exercise_log.utime = None
            exercise_log.stime = None
            pass
        elif status == 1:
            exercise_log.status = 1
            exercise_log.stime = None
            pass
        elif status == 2:
            exercise_log.status = 2
            exercise_log.stime = datetime.now()
            pass
        else:
            return False

        db.commit()
        return True

    except Exception as e:
        db.rollback()
        raise e


def get_exercise_log(
    db: Session, log_id: int, current_user: TntStudent
) -> Optional[TntExerciseLog]:
    """
    获取练习记录

    Args:
        db: 数据库会话
        log_id: 练习记录ID
        current_user: 当前用户

    Returns:
        练习记录或None
    """
    return (
        db.query(TntExerciseLog)
        .filter(
            TntExerciseLog.id == log_id,
            TntExerciseLog.sid == current_user.id,
            TntExerciseLog.tenant_id == current_user.tenant_id,
            TntExerciseLog.active == 1,
        )
        .first()
    )
