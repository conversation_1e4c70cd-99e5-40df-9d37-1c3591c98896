#!/usr/bin/env python3
"""
测试删除整体评论接口的简单脚本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_ENDPOINT = f"{BASE_URL}/api/v1/scene/comments"

def test_delete_comments():
    """测试删除整体评论接口"""
    
    # 测试数据
    test_data = {
        "elid": 123  # 练习情况ID
    }
    
    # 请求头（需要替换为实际的token）
    headers = {
        "Authorization": "Bearer YOUR_ACCESS_TOKEN",
        "Content-Type": "application/json"
    }
    
    print(f"测试删除整体评论接口: {API_ENDPOINT}")
    print(f"请求数据: {json.dumps(test_data, indent=2)}")
    
    try:
        # 发送DELETE请求
        response = requests.delete(
            API_ENDPOINT,
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"成功响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False

def test_api_structure():
    """测试API结构是否正确"""
    print("\n=== API接口结构测试 ===")
    print("接口路径: DELETE /api/v1/scene/comments")
    print("请求体结构:")
    print("  {")
    print("    \"elid\": int  // 练习情况ID")
    print("  }")
    print("\n成功响应结构:")
    print("  {")
    print("    \"message\": \"整体评论删除成功\"")
    print("  }")
    print("\n错误响应:")
    print("  404: 练习情况不存在或无权访问")
    print("  500: 服务器内部错误")

if __name__ == "__main__":
    print("=== 删除整体评论接口测试 ===")
    test_api_structure()
    
    print("\n=== 实际请求测试 ===")
    print("注意: 需要替换 YOUR_ACCESS_TOKEN 为实际的访问令牌")
    print("注意: 需要确保服务器正在运行")
    
    # 如果需要实际测试，取消下面的注释
    # test_delete_comments()
