from pydantic import MySQLDsn
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    PROJECT_NAME: str = "TMAI-TCamp-Rest"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    HOST: str = "0.0.0.0"
    PORT: int
    DEBUG: bool = True
    ENVIRONMENT: str

    SECRET_KEY: str
    ALGORITHM: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    DATABASE_URL: MySQLDsn

    start_time: str | None = None

    # 新增 OSS 配置
    OSS_ENDPOINT: str
    OSS_BUCKET: str
    OSS_BASE_URL: str
    OSS_ACCESS_KEY_ID: str
    OSS_ACCESS_KEY_SECRET: str

    # 语音听写配置
    STT_API_KEY: str
    STT_API_SECRET: str
    STT_API_HOST: str
    STT_APP_ID: str

    # 语音合成配置 (火山引擎TTS)
    TTS_APP_ID: str
    TTS_ACCESS_TOKEN: str
    TTS_CLUSTER: str
    TTS_HOST: str = "openspeech.bytedance.com"

    # 根据环境设置默认日志级别
    @property
    def log_level(self) -> str:
        if self.ENVIRONMENT.lower() in ("development", "dev", "local"):
            return "DEBUG"
        elif self.ENVIRONMENT.lower() in ("testing", "test"):
            return "DEBUG"
        else:  # production
            return "INFO"

    LOG_FORMAT: str = "[%(levelname)s]\t%(asctime)s\t%(name)s - %(message)s"
    LOG_DATE_FORMAT: str = "%Y-%m-%d %H:%M:%S"
    LOG_FILE_SIZE: int = 10485760  # 10MB
    LOG_BACKUP_COUNT: int = 5

    model_config = {"case_sensitive": True, "env_file": ".env"}


settings = Settings() # type: ignore
