# Database settings
DATABASE_URL=mysql://user:password@localhost:3306/tmai

# JWT settings
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=300

# OSS settings
OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
OSS_BUCKET=your-bucket-name
OSS_BASE_URL=https://your-bucket.oss-cn-beijing.aliyuncs.com
OSS_ACCESS_KEY_ID=your-access-key-id
OSS_ACCESS_KEY_SECRET=your-access-key-secret

# Speech to Text settings
STT_APP_ID=
STT_API_KEY=
STT_API_SECRET=
STT_API_HOST=ws-api.xfyun.cn

# Text to Speech settings (火山引擎TTS)
TTS_APP_ID=your-tts-app-id
TTS_ACCESS_TOKEN=your-tts-access-token
TTS_CLUSTER=your-tts-cluster
TTS_HOST=openspeech.bytedance.com

# Server settings
PROJECT_NAME=TMAI-Console-Rest
VERSION=1.0.0
HOST=0.0.0.0
PORT=8000
DEBUG=True
ENVIRONMENT=prd