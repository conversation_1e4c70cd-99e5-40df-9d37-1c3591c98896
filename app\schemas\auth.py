from pydantic import BaseModel, Field


# ======================== Request Models 请求模型 ========================

class LoginRequest(BaseModel):
    """登录请求"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

    class Config:
        from_attributes = True


class PasswordUpdateRequest(BaseModel):
    """密码更新请求"""

    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., description="新密码")

    class Config:
        from_attributes = True


# ======================== Response Models 响应模型 ========================

class LoginResponse(BaseModel):
    """登录响应"""

    token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    tenant_id: int = Field(..., description="租户ID")

    class Config:
        from_attributes = True


class LogoutResponse(BaseModel):
    """登出响应"""

    message: str = Field("登出成功", description="响应消息")

    class Config:
        from_attributes = True


class ProfileResponse(BaseModel):
    """用户个人信息响应"""

    uid: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    name: str = Field(..., description="姓名")
    tenant_id: int = Field(..., description="租户ID")
    gender: int = Field(..., description="性别（0：未知；1：男；2：女）")

    class Config:
        from_attributes = True


# ======================== General Models 通用模型 ========================

class UserTokenPayload(BaseModel):
    """用户token payload schema"""

    uid: int = Field(..., description="用户ID")
    utype: str = Field(..., description="用户类型（tnt）")
    exp: int = Field(..., description="过期时间")

    class Config:
        from_attributes = True

