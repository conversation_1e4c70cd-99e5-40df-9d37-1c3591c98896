import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.config import settings
from app.schemas import misc as misc_schema
from app.services import auth as auth_service
from app.services import misc as misc_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stt/wsurl", response_model=misc_schema.WebSocketUrlResponse)
def get_speech_to_text_ws_url(
    current_user = Depends(auth_service.get_current_user)
) -> Any:
    """
    获取语音听写 WebSocket URL
    
    ## 功能描述
    该接口返回用于语音听写的 WebSocket URL，客户端可以通过该 URL 连接到语音听写服务。
    
    ## 请求参数
    无需额外参数，仅需要有效的用户身份令牌。
    
    ## 响应
    - **200**: 成功返回语音听写 WebSocket URL
        - 返回类型: WebSocketUrlResponse
        - 包含以下字段：
            - wsUrl: WebSocket连接URL（包含鉴权参数）
            - appId: 语音听写应用ID
    
    ## 权限要求
    - 需要有效的用户身份令牌
    
    ## 业务规则
    - 根据讯飞开放平台的规则生成带鉴权参数的 WebSocket URL
    - URL包含必要的鉴权信息，客户端可直接使用
    - 生成的URL具有时效性，建议及时使用
    
    ## 错误处理
    - **500**: 服务器内部错误
        - 语音听写服务配置错误
        - 鉴权参数生成失败
    """
    try:
        ws_url = misc_service.get_speech_to_text_ws_url()
        return {
            "wsUrl": ws_url,
            "appId": settings.STT_APP_ID
        }
    except Exception as e:
        logger.error(f"获取语音听写 WebSocket URL 失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取语音听写 WebSocket URL 失败"
        )