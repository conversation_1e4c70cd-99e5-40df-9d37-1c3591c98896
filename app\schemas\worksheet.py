from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

# ======================== Request Models 请求模型 ========================


class QuestionCommentRequest(BaseModel):
    """问题点评请求"""

    elid: int = Field(..., description="练习情况ID")
    qid: int = Field(..., description="问题ID")
    answer: str = Field(..., description="作答内容")

    class Config:
        from_attributes = True


class QuestionRetryRequest(BaseModel):
    """问题重新练习请求"""

    elid: int = Field(..., description="练习情况ID")
    qid: int = Field(..., description="问题ID")

    class Config:
        from_attributes = True


class QuestionDraftItem(BaseModel):
    """问题草稿项"""

    qid: int = Field(..., description="问题ID")
    draft: str = Field(..., description="草稿")

    class Config:
        from_attributes = True


class QuestionDraftBatchRequest(BaseModel):
    """批量更新问题草稿请求"""

    elid: int = Field(..., description="练习情况ID")
    list: List[QuestionDraftItem] = Field(..., description="问题草稿列表")

    class Config:
        from_attributes = True


# ======================== Response Models 响应模型 ========================


class WorksheetBasicResponse(BaseModel):
    """作业单基本信息响应"""

    title: str = Field(..., description="标题")
    pic: Optional[str] = Field(None, description="图片URL")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="时长（分钟）")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    report: Optional[str] = Field(None, description="整体点评报告URL")
    btime: Optional[datetime] = Field(None, description="开始练习时间")
    stime: Optional[datetime] = Field(None, description="提交练习时间")
    utime: Optional[datetime] = Field(None, description="上次练习时间")
    status: int = Field(..., description="练习状态（0：待练习；1：练习中；2：已提交）")
    eid: int = Field(..., description="练习ID")
    elid: Optional[int] = Field(None, description="练习情况ID")
    tid: Optional[int] = Field(None, description="老师ID")
    tname: Optional[str] = Field(None, description="老师姓名")
    tavatar: Optional[str] = Field(None, description="老师头像URL")

    class Config:
        from_attributes = True


class WorksheetUnitResponse(BaseModel):
    """作业单单元响应"""

    id: int = Field(..., description="单元ID")
    name: str = Field(..., description="单元名称")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")

    class Config:
        from_attributes = True


class WorksheetUnitsResponse(BaseModel):
    """作业单单元列表响应"""

    unit_list: List[WorksheetUnitResponse] = Field(
        default_factory=list, description="单元列表"
    )

    class Config:
        from_attributes = True


class WorksheetDetailResponse(BaseModel):
    """作业单详情响应"""

    title: str = Field(..., description="标题")
    pic: Optional[str] = Field(None, description="图片URL")
    intro: Optional[str] = Field(None, description="简介")
    duration: Optional[int] = Field(None, description="时长（分钟）")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    report: Optional[str] = Field(None, description="整体点评报告URL")
    btime: Optional[datetime] = Field(None, description="开始练习时间")
    stime: Optional[datetime] = Field(None, description="提交练习时间")
    utime: Optional[datetime] = Field(None, description="上次练习时间")
    status: int = Field(..., description="练习状态（0：待练习；1：练习中；2：已提交）")
    unit_list: List[WorksheetUnitResponse] = Field(
        default_factory=list, description="单元列表"
    )
    eid: int = Field(..., description="练习ID")
    elid: Optional[int] = Field(None, description="练习情况ID")

    class Config:
        from_attributes = True


class FrameworkModuleResponse(BaseModel):
    """理论模块响应"""

    name: str = Field(..., description="理论模块名称")

    class Config:
        from_attributes = True


class FrameworkResponse(BaseModel):
    """理论框架响应"""

    name: str = Field(..., description="理论框架名称")
    logo: Optional[str] = Field(None, description="理论框架logo URL")
    module_list: List[FrameworkModuleResponse] = Field(
        default_factory=list, description="理论模块列表"
    )

    class Config:
        from_attributes = True


class QuestionGuideResponse(BaseModel):
    """问题作答指南响应"""

    title: str = Field(..., description="指南标题")
    details: str = Field(..., description="指南详情")

    class Config:
        from_attributes = True


class UnitQuestionResponse(BaseModel):
    """单元问题响应"""

    id: int = Field(..., description="问题ID")
    title: str = Field(..., description="问题标题")
    bgtext: Optional[str] = Field(None, description="背景文字")
    bgvideo: Optional[str] = Field(None, description="背景视频URL")
    draft: Optional[str] = Field(None, description="作答草稿")
    answer: Optional[str] = Field(None, description="已提交作答")
    comment: Optional[str] = Field(None, description="AI点评")
    framework_list: List[FrameworkResponse] = Field(
        default_factory=list, description="理论框架列表"
    )
    guide_list: List[QuestionGuideResponse] = Field(
        default_factory=list, description="作答指南列表"
    )

    class Config:
        from_attributes = True


class UnitQuestionsResponse(BaseModel):
    """单元问题列表响应"""

    question_list: List[UnitQuestionResponse] = Field(
        default_factory=list, description="问题列表"
    )

    class Config:
        from_attributes = True
