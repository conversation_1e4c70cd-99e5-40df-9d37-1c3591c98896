from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

# ======================== Request Models 请求模型 ========================


class ExerciseLogCreateRequest(BaseModel):
    """创建练习记录请求"""

    cid: int = Field(..., description="班级ID")
    eid: int = Field(..., description="练习ID")

    class Config:
        from_attributes = True


# ======================== Response Models 响应模型 ========================


class ExerciseLogCreateResponse(BaseModel):
    """创建练习记录响应"""

    id: int = Field(..., description="练习情况ID")
    btime: Optional[datetime] = Field(None, description="开始练习时间")

    class Config:
        from_attributes = True
