import logging
import uuid
from typing import Any, Dict, Union

from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class ExceptionMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            return await call_next(request)
        except Exception as exc:
            return await self.handle_exception(exc)

    @staticmethod
    async def handle_exception(exc: Exception) -> JSONResponse:
        # 生成唯一的错误ID
        error_id = str(uuid.uuid4())
        
        if isinstance(exc, RequestValidationError):
            logger.error("[ERROR_ID: %s] Request validation error: %s", error_id, exc.errors())
            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content={
                    "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
                    "message": "参数验证错误，请联系管理员！",
                    "error_id": error_id,
                },
            )
        elif isinstance(exc, ValidationError):
            logger.error("[ERROR_ID: %s] Pydantic validation error: %s", error_id, exc.errors())
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "code": status.HTTP_500_INTERNAL_SERVER_ERROR,
                    "message": "数据验证错误，请联系管理员！",
                    "error_id": error_id,
                },
            )
        elif isinstance(exc, SQLAlchemyError):
            logger.error("[ERROR_ID: %s] Database error: %s", error_id, str(exc))
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "code": status.HTTP_500_INTERNAL_SERVER_ERROR,
                    "message": "数据库错误，请联系管理员！",
                    "error_id": error_id,
                },
            )
        else:
            logger.error("[ERROR_ID: %s] Unhandled server error: %s", error_id, str(exc), exc_info=True)
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "code": status.HTTP_500_INTERNAL_SERVER_ERROR,
                    "message": "服务器错误，请联系管理员！",
                    "error_id": error_id,
                },
            )


def create_error_response(
    status_code: int, message: str, details: Union[str, Dict[str, Any], None] = None
) -> Dict[str, Any]:
    return {"code": status_code, "message": message, "details": details}
