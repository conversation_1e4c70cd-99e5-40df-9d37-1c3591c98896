from pydantic import BaseModel, Field


# ======================== Response Models 响应模型 ========================

class MessageResponse(BaseModel):
    """消息响应Schema类"""

    message: str = Field(..., description="响应消息")

    class Config:
        from_attributes = True


class StatusResponse(BaseModel):
    """状态响应Schema类"""

    status: str = Field(..., description="操作状态")

    class Config:
        from_attributes = True
