from sqlalchemy import DateTime, ForeignKey, Integer, String, Text, func
from datetime import datetime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, List

from app.db.base_class import Base


# System Tables
class SysUser(Base):
    """用户表"""
    __tablename__ = "sys_user"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="用户ID")
    username: Mapped[str] = mapped_column(String(45), nullable=False, unique=True, comment="用户名")
    passwd: Mapped[str] = mapped_column(String(255), nullable=False, comment="密码")
    token_version: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="token版本，用于登出")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    admin: Mapped[Optional["SysAdmin"]] = relationship("SysAdmin", back_populates="user", uselist=False)
    tenant_admins: Mapped[List["TntAdmin"]] = relationship("TntAdmin", back_populates="user")
    students: Mapped[List["TntStudent"]] = relationship("TntStudent", back_populates="user")

class SysAdmin(Base):
    """系统管理员表"""
    __tablename__ = "sys_admin"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="系统管理员ID")
    uid: Mapped[int] = mapped_column(Integer, ForeignKey("sys_user.id"), nullable=False, unique=True, comment="用户ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="姓名")
    role: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="角色（0：超级管理员；1：管理员）")

    # Relationships
    user: Mapped["SysUser"] = relationship("SysUser", back_populates="admin")


class SysBconf(Base):
    """全局机器人设置表"""
    __tablename__ = "sys_bconf"

    key: Mapped[str] = mapped_column(String(45), primary_key=True, comment="AI设置key")
    bid: Mapped[int] = mapped_column(Integer, ForeignKey("sys_bot.id"), nullable=False, comment="机器人ID")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")

    # Relationships
    bot: Mapped["SysBot"] = relationship("SysBot", back_populates="global_configs")


class SysBot(Base):
    """机器人表"""
    __tablename__ = "sys_bot"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="机器人ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="名称")
    api_endpoint: Mapped[Optional[str]] = mapped_column(String(512), comment="API 路径")
    api_key: Mapped[Optional[str]] = mapped_column(String(512), comment="API key")
    sys_prompt: Mapped[Optional[str]] = mapped_column(Text, comment="系统提示词模板")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    global_configs: Mapped[List["SysBconf"]] = relationship("SysBconf", back_populates="bot")
    tenant_configs: Mapped[List["TntBconf"]] = relationship("TntBconf", back_populates="bot")


class SysTenant(Base):
    """租户表"""
    __tablename__ = "sys_tenant"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="租户ID")
    code: Mapped[str] = mapped_column(String(45), nullable=False, unique=True, comment="租户代号")
    name: Mapped[Optional[str]] = mapped_column(String(255), comment="名称")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    admins: Mapped[List["TntAdmin"]] = relationship("TntAdmin", back_populates="tenant")
    admin_classes: Mapped[List["TntAdminClass"]] = relationship("TntAdminClass", back_populates="tenant")
    bot_configs: Mapped[List["TntBconf"]] = relationship("TntBconf", back_populates="tenant")
    characters: Mapped[List["TntCharacter"]] = relationship("TntCharacter", back_populates="tenant")
    classes: Mapped[List["TntClass"]] = relationship("TntClass", back_populates="tenant")
    cues: Mapped[List["TntCue"]] = relationship("TntCue", back_populates="tenant")
    exercises: Mapped[List["TntExercise"]] = relationship("TntExercise", back_populates="tenant")
    exercise_logs: Mapped[List["TntExerciseLog"]] = relationship("TntExerciseLog", back_populates="tenant")
    frameworks: Mapped[List["TntFramework"]] = relationship("TntFramework", back_populates="tenant")
    lines: Mapped[List["TntLine"]] = relationship("TntLine", back_populates="tenant")
    modules: Mapped[List["TntModule"]] = relationship("TntModule", back_populates="tenant")
    plans: Mapped[List["TntPlan"]] = relationship("TntPlan", back_populates="tenant")
    questions: Mapped[List["TntQuestion"]] = relationship("TntQuestion", back_populates="tenant")
    scenes: Mapped[List["TntScene"]] = relationship("TntScene", back_populates="tenant")
    scene_characters: Mapped[List["TntSceneCharacter"]] = relationship("TntSceneCharacter", back_populates="tenant")
    scene_guides: Mapped[List["TntSceneGuide"]] = relationship("TntSceneGuide", back_populates="tenant")
    scene_speeches: Mapped[List["TntSceneSpeech"]] = relationship("TntSceneSpeech", back_populates="tenant")
    speech_jobs: Mapped[List["TntSpeechJob"]] = relationship("TntSpeechJob", back_populates="tenant")
    students: Mapped[List["TntStudent"]] = relationship("TntStudent", back_populates="tenant")
    subjects: Mapped[List["TntSubject"]] = relationship("TntSubject", back_populates="tenant")
    teachers: Mapped[List["TntTeacher"]] = relationship("TntTeacher", back_populates="tenant")
    units: Mapped[List["TntUnit"]] = relationship("TntUnit", back_populates="tenant")
    worksheets: Mapped[List["TntWorksheet"]] = relationship("TntWorksheet", back_populates="tenant")
    worksheet_answers: Mapped[List["TntWorksheetAnswer"]] = relationship("TntWorksheetAnswer", back_populates="tenant")
    worksheet_asms: Mapped[List["TntWorksheetAsm"]] = relationship("TntWorksheetAsm", back_populates="tenant")


# Tenant Tables

class TntAdmin(Base):
    """租户管理员表"""
    __tablename__ = "tnt_admin"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="租户管理员ID")
    uid: Mapped[int] = mapped_column(Integer, ForeignKey("sys_user.id"), nullable=False, comment="用户ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="姓名")
    role: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="角色（1：管理员, 2：教学运营人员）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="admins")
    user: Mapped["SysUser"] = relationship("SysUser", back_populates="tenant_admins")
    admin_classes: Mapped[List["TntAdminClass"]] = relationship("TntAdminClass", back_populates="admin")


class TntAdminClass(Base):
    """跟班关系表"""
    __tablename__ = "tnt_admin_class"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    aid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_admin.id"), nullable=False, comment="教学运营人员ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="admin_classes")
    admin: Mapped["TntAdmin"] = relationship("TntAdmin", back_populates="admin_classes")
    class_: Mapped["TntClass"] = relationship("TntClass", back_populates="admin_classes")


class TntBconf(Base):
    """租户机器人设置表"""
    __tablename__ = "tnt_bconf"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), primary_key=True, nullable=False, comment="租户ID")
    key: Mapped[str] = mapped_column(String(45), primary_key=True, nullable=False, comment="AI设置key")
    bid: Mapped[int] = mapped_column(Integer, ForeignKey("sys_bot.id"), nullable=False, comment="机器人ID")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="bot_configs")
    bot: Mapped["SysBot"] = relationship("SysBot", back_populates="tenant_configs")


class TntCharacter(Base):
    """人物表"""
    __tablename__ = "tnt_character"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="人物ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="姓名")
    gender: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    avatar: Mapped[Optional[str]] = mapped_column(String(255), comment="头像URL")
    profile: Mapped[str] = mapped_column(Text, nullable=False, comment="人物资料")
    timbre_type: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="音色类型（0：未设置；1：火山引擎；）")
    timbre: Mapped[Optional[str]] = mapped_column(String(255), comment="语音音色")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    pv_profile: Mapped[Optional[str]] = mapped_column(Text, comment="人物资料（提示词变量，默认为profile，后台用）")
    pv_ability: Mapped[Optional[str]] = mapped_column(Text, comment="人物能力（提示词变量，后台用）")
    pv_restriction: Mapped[Optional[str]] = mapped_column(Text, comment="人物限制（提示词变量，后台用）")
    published: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="是否发布（0：未发布；1：已发布）")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="characters")
    cues: Mapped[List["TntCue"]] = relationship("TntCue", back_populates="character")
    lines: Mapped[List["TntLine"]] = relationship("TntLine", back_populates="character")
    scene_characters: Mapped[List["TntSceneCharacter"]] = relationship("TntSceneCharacter", back_populates="character")
    scene_speeches: Mapped[List["TntSceneSpeech"]] = relationship("TntSceneSpeech", back_populates="character")
    speech_jobs: Mapped[List["TntSpeechJob"]] = relationship("TntSpeechJob", back_populates="character")


class TntClass(Base):
    """班级表"""
    __tablename__ = "tnt_class"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="班级ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="名称")
    pic: Mapped[Optional[str]] = mapped_column(String(255), comment="图片URL")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="描述")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    btime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="开始时间")
    etime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="结束时间")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="classes")
    admin_classes: Mapped[List["TntAdminClass"]] = relationship("TntAdminClass", back_populates="class_")
    class_exercises: Mapped[List["TntClassExercise"]] = relationship("TntClassExercise", back_populates="class_")
    class_students: Mapped[List["TntClassStudent"]] = relationship("TntClassStudent", back_populates="class_")
    exercise_logs: Mapped[List["TntExerciseLog"]] = relationship("TntExerciseLog", back_populates="class_")


class TntClassExercise(Base):
    """班级练习关系表"""
    __tablename__ = "tnt_class_exercise"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")
    eid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    tid: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="老师ID")
    depend: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant")
    class_: Mapped["TntClass"] = relationship("TntClass", back_populates="class_exercises")
    exercise: Mapped["TntExercise"] = relationship("TntExercise", back_populates="class_exercises")


class TntClassStudent(Base):
    """班级学员关系表"""
    __tablename__ = "tnt_class_student"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_student.id"), nullable=False, comment="学员ID")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant")
    class_: Mapped["TntClass"] = relationship("TntClass", back_populates="class_students")
    student: Mapped["TntStudent"] = relationship("TntStudent", back_populates="class_students")


class TntCue(Base):
    """剧本提示表"""
    __tablename__ = "tnt_cue"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="剧本提示ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_scene.id"), nullable=False, comment="场景ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="人物ID（触发人物）")
    content: Mapped[str] = mapped_column(String(1024), nullable=False, comment="触发内容（当这个人物发言为此内容时触发）")
    serial: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否顺序发言（0：并行发言；1：顺序发言）")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="cues")
    scene: Mapped["TntScene"] = relationship("TntScene", back_populates="cues")
    character: Mapped["TntCharacter"] = relationship("TntCharacter", back_populates="cues")
    lines: Mapped[List["TntLine"]] = relationship("TntLine", back_populates="cue")



class TntExercise(Base):
    """练习表"""
    __tablename__ = "tnt_exercise"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="练习ID")
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="标题")
    type: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="类型（1：作业单；2：角色扮演；）")
    pic: Mapped[Optional[str]] = mapped_column(String(255), comment="图片URL")
    intro: Mapped[Optional[str]] = mapped_column(String(255), comment="简介")
    duration: Mapped[Optional[int]] = mapped_column(Integer, comment="预估练习时长（分钟）")
    version: Mapped[Optional[str]] = mapped_column(String(255), comment="版本")
    bgtext: Mapped[Optional[str]] = mapped_column(Text, comment="背景文字")
    bgvideo: Mapped[Optional[str]] = mapped_column(String(255), comment="背景视频URL")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    published: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="是否发布（0：未发布；1：已发布）")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="exercises")
    class_exercises: Mapped[List["TntClassExercise"]] = relationship("TntClassExercise", back_populates="exercise")
    exercise_logs: Mapped[List["TntExerciseLog"]] = relationship("TntExerciseLog", back_populates="exercise")
    plan_exercises: Mapped[List["TntPlanExercise"]] = relationship("TntPlanExercise", back_populates="exercise")
    scenes: Mapped[List["TntScene"]] = relationship("TntScene", back_populates="exercise")
    worksheets: Mapped[List["TntWorksheet"]] = relationship("TntWorksheet", back_populates="exercise")


class TntExerciseLog(Base):
    """练习情况表"""
    __tablename__ = "tnt_exercise_log"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="练习情况ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_class.id"), nullable=False, comment="班级ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_student.id"), nullable=False, comment="学员ID")
    eid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    comments: Mapped[str] = mapped_column(Text, nullable=True, comment="AI整体点评")
    report: Mapped[Optional[str]] = mapped_column(String(255), comment="整体点评报告URL")
    btime: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始练习时间")
    stime: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="提交练习时间")
    utime: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="上次更新时间")
    status: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="练习状态（0：待练习；1：练习中；2：已提交；）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="exercise_logs")
    class_: Mapped["TntClass"] = relationship("TntClass", back_populates="exercise_logs")
    student: Mapped["TntStudent"] = relationship("TntStudent", back_populates="exercise_logs")
    exercise: Mapped["TntExercise"] = relationship("TntExercise", back_populates="exercise_logs")
    scene_speeches: Mapped[List["TntSceneSpeech"]] = relationship("TntSceneSpeech", back_populates="exercise_log")
    speech_jobs: Mapped[List["TntSpeechJob"]] = relationship("TntSpeechJob", back_populates="exercise_log")
    worksheet_answers: Mapped[List["TntWorksheetAnswer"]] = relationship("TntWorksheetAnswer", back_populates="exercise_log")


class TntFramework(Base):
    """理论框架表"""
    __tablename__ = "tnt_framework"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="理论框架ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="理论框架名称（如：PMI人才三角等）")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="详细描述")
    logo: Mapped[Optional[str]] = mapped_column(String(255), comment="Logo URL")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="frameworks")
    modules: Mapped[List["TntModule"]] = relationship("TntModule", back_populates="framework")


class TntLine(Base):
    """台词表"""
    __tablename__ = "tnt_line"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="台词ID")
    cueid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_cue.id"), nullable=False, comment="剧本提示ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="人物ID（发言人物）")
    pv_topic: Mapped[str] = mapped_column(String(512), nullable=False, comment="发言主题（提示词变量，后台用）")
    pv_ability: Mapped[str] = mapped_column(Text, nullable=False, comment="人物能力（提示词变量，后台用）")
    pv_restriction: Mapped[str] = mapped_column(Text, nullable=False, comment="人物限制（提示词变量，后台用）")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="lines")
    cue: Mapped["TntCue"] = relationship("TntCue", back_populates="lines")
    character: Mapped["TntCharacter"] = relationship("TntCharacter", back_populates="lines")


class TntModule(Base):
    """理论模块表"""
    __tablename__ = "tnt_module"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="理论模块ID")
    fid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_framework.id"), nullable=False, comment="理论框架ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="理论模块名称")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="详细描述")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="modules")
    framework: Mapped["TntFramework"] = relationship("TntFramework", back_populates="modules")
    question_modules: Mapped[List["TntQuestionModule"]] = relationship("TntQuestionModule", back_populates="module")


class TntPlan(Base):
    """计划表"""
    __tablename__ = "tnt_plan"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="计划ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="名称")
    pic: Mapped[Optional[str]] = mapped_column(String(255), comment="图片URL")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="描述")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="plans")
    plan_exercises: Mapped[List["TntPlanExercise"]] = relationship("TntPlanExercise", back_populates="plan")


class TntPlanExercise(Base):
    """计划练习关系表"""
    __tablename__ = "tnt_plan_exercise"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    pid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_plan.id"), nullable=False, comment="计划ID")
    eid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    depend: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否依赖前一个练习结束（0：不依赖；1：依赖）")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant")
    plan: Mapped["TntPlan"] = relationship("TntPlan", back_populates="plan_exercises")
    exercise: Mapped["TntExercise"] = relationship("TntExercise", back_populates="plan_exercises")


class TntQuestion(Base):
    """问题表"""
    __tablename__ = "tnt_question"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="问题ID")
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="标题")
    bgtext: Mapped[Optional[str]] = mapped_column(Text, comment="背景文字")
    bgvideo: Mapped[Optional[str]] = mapped_column(String(255), comment="背景视频URL")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    pv_skills: Mapped[Optional[str]] = mapped_column(Text, comment="点评能力（提示词变量，后台用）")
    pv_rules: Mapped[Optional[str]] = mapped_column(Text, comment="点评细则（提示词变量，后台用）")
    pv_formats: Mapped[Optional[str]] = mapped_column(Text, comment="点评格式（提示词变量，后台用）")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="是否有效（0：失效；1：有效）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="questions")
    question_guides: Mapped[List["TntQuestionGuide"]] = relationship("TntQuestionGuide", back_populates="question")
    question_modules: Mapped[List["TntQuestionModule"]] = relationship("TntQuestionModule", back_populates="question")
    question_subjects: Mapped[List["TntQuestionSubject"]] = relationship("TntQuestionSubject", back_populates="question")
    worksheet_answers: Mapped[List["TntWorksheetAnswer"]] = relationship("TntWorksheetAnswer", back_populates="question")
    worksheet_asms: Mapped[List["TntWorksheetAsm"]] = relationship("TntWorksheetAsm", back_populates="question")


class TntQuestionGuide(Base):
    """问题作答指南表"""
    __tablename__ = "tnt_question_guide"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="作答指南ID")
    qid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="指南标题")
    details: Mapped[str] = mapped_column(Text, nullable=False, comment="指南详情")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant")
    question: Mapped["TntQuestion"] = relationship("TntQuestion", back_populates="question_guides")


class TntQuestionModule(Base):
    """问题-理论模块关系表"""
    __tablename__ = "tnt_question_module"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    qid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    mid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_module.id"), nullable=False, comment="理论模块ID")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant")
    question: Mapped["TntQuestion"] = relationship("TntQuestion", back_populates="question_modules")
    module: Mapped["TntModule"] = relationship("TntModule", back_populates="question_modules")


class TntQuestionSubject(Base):
    """问题-主题关系表"""
    __tablename__ = "tnt_question_subject"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    qid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_subject.id"), nullable=False, comment="主题ID")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant")
    question: Mapped["TntQuestion"] = relationship("TntQuestion", back_populates="question_subjects")
    subject: Mapped["TntSubject"] = relationship("TntSubject", back_populates="question_subjects")


class TntScene(Base):
    """场景表"""
    __tablename__ = "tnt_scene"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="场景ID")
    eid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")
    pv_scripts: Mapped[str] = mapped_column(Text, nullable=False, comment="剧情脚本（提示词变量，后台用）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="scenes")
    exercise: Mapped["TntExercise"] = relationship("TntExercise", back_populates="scenes")
    cues: Mapped[List["TntCue"]] = relationship("TntCue", back_populates="scene")
    scene_characters: Mapped[List["TntSceneCharacter"]] = relationship("TntSceneCharacter", back_populates="scene")
    scene_guides: Mapped[List["TntSceneGuide"]] = relationship("TntSceneGuide", back_populates="scene")


class TntSceneCharacter(Base):
    """场景人物关系表"""
    __tablename__ = "tnt_scene_character"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_scene.id"), nullable=False, comment="场景ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="人物ID")
    played: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="是否为学员扮演（0：否；1：是）")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="scene_characters")
    scene: Mapped["TntScene"] = relationship("TntScene", back_populates="scene_characters")
    character: Mapped["TntCharacter"] = relationship("TntCharacter", back_populates="scene_characters")


class TntSceneGuide(Base):
    """场景练习指南表"""
    __tablename__ = "tnt_scene_guide"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="指南ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_scene.id"), nullable=False, comment="场景ID")
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="指南标题")
    details: Mapped[str] = mapped_column(Text, nullable=False, comment="指南详情")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="scene_guides")
    scene: Mapped["TntScene"] = relationship("TntScene", back_populates="scene_guides")


class TntSceneSpeech(Base):
    """发言表"""
    __tablename__ = "tnt_scene_speech"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="发言ID")
    elid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise_log.id"), nullable=False, comment="练习情况ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="角色ID")
    played: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="是否是学员扮演（0：否；1：是）")
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="发言内容")
    to_cids: Mapped[Optional[str]] = mapped_column(String(255), comment="@列表（人物ID列表，用逗号分隔，可为空）")
    status: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="发言状态（0：未处理；1：处理中；2：已处理）")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="发言时间")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="scene_speeches")
    exercise_log: Mapped["TntExerciseLog"] = relationship("TntExerciseLog", back_populates="scene_speeches")
    character: Mapped["TntCharacter"] = relationship("TntCharacter", back_populates="scene_speeches")
    speech_jobs: Mapped[List["TntSpeechJob"]] = relationship("TntSpeechJob", back_populates="scene_speech")


class TntStudent(Base):
    """学员表"""
    __tablename__ = "tnt_student"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="学员ID")
    uid: Mapped[int] = mapped_column(Integer, ForeignKey("sys_user.id"), nullable=False, comment="用户ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="姓名")
    gender: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="students")
    user: Mapped["SysUser"] = relationship("SysUser", back_populates="students")
    class_students: Mapped[List["TntClassStudent"]] = relationship("TntClassStudent", back_populates="student")
    exercise_logs: Mapped[List["TntExerciseLog"]] = relationship("TntExerciseLog", back_populates="student")


class TntSubject(Base):
    """主题表"""
    __tablename__ = "tnt_subject"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="主题ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="主题名称（如：项目管理、领导力、沟通技巧等）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="subjects")
    question_subjects: Mapped[List["TntQuestionSubject"]] = relationship("TntQuestionSubject", back_populates="subject")


class TntTeacher(Base):
    """老师表"""
    __tablename__ = "tnt_teacher"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="老师ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="姓名")
    gender: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="性别（0：未知；1：男；2：女；）")
    avatar: Mapped[str] = mapped_column(String(255), nullable=False, comment="头像URL")
    intro: Mapped[Optional[str]] = mapped_column(Text, comment="简介")
    notes: Mapped[Optional[str]] = mapped_column(String(512), comment="备注")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="teachers")


class TntUnit(Base):
    """单元模块表"""
    __tablename__ = "tnt_unit"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="单元模块ID")
    wid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_worksheet.id"), nullable=False, comment="作业单ID")
    name: Mapped[str] = mapped_column(String(255), nullable=False, comment="名称")
    bgtext: Mapped[Optional[str]] = mapped_column(Text, comment="背景文字")
    bgvideo: Mapped[Optional[str]] = mapped_column(String(255), comment="背景视频URL")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="units")
    worksheet: Mapped["TntWorksheet"] = relationship("TntWorksheet", back_populates="units")
    worksheet_asms: Mapped[List["TntWorksheetAsm"]] = relationship("TntWorksheetAsm", back_populates="unit")


class TntWorksheet(Base):
    """作业单表"""
    __tablename__ = "tnt_worksheet"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="作业单ID")
    eid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise.id"), nullable=False, comment="练习ID")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="worksheets")
    exercise: Mapped["TntExercise"] = relationship("TntExercise", back_populates="worksheets")
    units: Mapped[List["TntUnit"]] = relationship("TntUnit", back_populates="worksheet")
    worksheet_asms: Mapped[List["TntWorksheetAsm"]] = relationship("TntWorksheetAsm", back_populates="worksheet")


class TntWorksheetAnswer(Base):
    """作业单作答表"""
    __tablename__ = "tnt_worksheet_answer"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="作业单作答ID")
    elid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise_log.id"), nullable=False, comment="练习情况ID")
    qid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    draft: Mapped[str] = mapped_column(Text, nullable=True, comment="草稿")
    answer: Mapped[str] = mapped_column(Text, nullable=True, comment="已提交作答")
    comment: Mapped[str] = mapped_column(Text, nullable=True, comment="AI点评内容")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="worksheet_answers")
    exercise_log: Mapped["TntExerciseLog"] = relationship("TntExerciseLog", back_populates="worksheet_answers")
    question: Mapped["TntQuestion"] = relationship("TntQuestion", back_populates="worksheet_answers")


class TntWorksheetAsm(Base):
    """作业单构成关系表"""
    __tablename__ = "tnt_worksheet_asm"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    wid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_worksheet.id"), nullable=False, comment="作业单ID")
    uid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_unit.id"), nullable=False, comment="单元ID")
    qid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_question.id"), nullable=False, comment="问题ID")
    priority: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="展示顺序（从小到大）")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="worksheet_asms")
    worksheet: Mapped["TntWorksheet"] = relationship("TntWorksheet", back_populates="worksheet_asms")
    unit: Mapped["TntUnit"] = relationship("TntUnit", back_populates="worksheet_asms")
    question: Mapped["TntQuestion"] = relationship("TntQuestion", back_populates="worksheet_asms")


class TntSpeechJob(Base):
    """发言任务表"""
    __tablename__ = "tnt_speech_job"

    tenant_id: Mapped[int] = mapped_column(Integer, ForeignKey("sys_tenant.id"), nullable=False, comment="租户ID")
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="发言任务ID")
    elid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_exercise_log.id"), nullable=False, comment="练习情况ID")
    sid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_scene_speech.id"), nullable=False, comment="发言ID")
    cid: Mapped[int] = mapped_column(Integer, ForeignKey("tnt_character.id"), nullable=False, comment="完成任务的人物ID")
    status: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="任务状态（0：未开始；1：进行中；）")
    vars: Mapped[Optional[str]] = mapped_column(Text, comment="额外参数（json格式，key/value形式，或为空），如：{\"topic\": \"xxxx\"}")
    sync: Mapped[int] = mapped_column(Integer, nullable=False, default=1, comment="任务执行方式（0:异步；1:同步；）")
    ctime: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间")

    # Relationships
    tenant: Mapped["SysTenant"] = relationship("SysTenant", back_populates="speech_jobs")
    exercise_log: Mapped["TntExerciseLog"] = relationship("TntExerciseLog", back_populates="speech_jobs")
    scene_speech: Mapped["TntSceneSpeech"] = relationship("TntSceneSpeech", back_populates="speech_jobs")
    character: Mapped["TntCharacter"] = relationship("TntCharacter", back_populates="speech_jobs")

