from fastapi import APIRouter, Depends, HTTPException, Response, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.models import TntStudent
from app.schemas.base import MessageResponse
from app.schemas.exercise import (
    ExerciseLogCreateRequest,
    ExerciseLogCreateResponse,
)
from app.services.auth import get_current_user
from app.services.exercise import (
    create_exercise_log,
    get_existing_exercise_log,
    update_exercise_log_status,
    validate_exercise_creation,
    validate_exercise_log_update,
)

router = APIRouter()


@router.post("/log", response_model=ExerciseLogCreateResponse)
def create_exercise_log_api(
    request: ExerciseLogCreateRequest,
    response: Response,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    创建练习记录

    ## 功能描述
    为某个练习创建练习记录，记录学员开始练习的时间和相关信息。
    如果发现满足tenant_id, cid, sid, eid条件的记录已经存在，则不创建新记录，直接返回已存在的记录。

    ## 请求参数
    - **request** (ExerciseLogCreateRequest): 创建练习记录请求信息，请求体
        - cid: 班级ID
        - eid: 练习ID

    ## 响应
    - **201 Created**: 新创建练习记录成功
        - 返回类型: ExerciseLogCreateResponse
        - 包含新创建的练习记录信息
            - id: 练习情况ID
            - btime: 开始练习时间
    - **200 OK**: 练习记录已存在
        - 返回类型: ExerciseLogCreateResponse
        - 包含已存在的练习记录信息
            - id: 练习情况ID
            - btime: 开始练习时间

    ## 权限要求
    - 需要有效的用户身份令牌

    ## 错误处理
    - **400**: 请求参数错误，班级练习关系或班级学员关系不存在
    - **401**: 未授权访问，当令牌无效或过期时返回此错误
    - **500**: 服务器内部错误
    """
    try:
        # 验证操作合法性
        if not validate_exercise_creation(db, request, current_user):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="班级练习关系或班级学员关系不存在，无法创建练习记录",
            )

        # 先查询是否已存在记录
        existing_log = get_existing_exercise_log(db, request, current_user)

        if existing_log:
            # 记录已存在，返回已存在的记录
            response.status_code = status.HTTP_200_OK
            return ExerciseLogCreateResponse(
                id=existing_log.id, btime=existing_log.btime
            )
        else:
            # 记录不存在，创建新记录
            response.status_code = status.HTTP_201_CREATED
            return create_exercise_log(db, request, current_user)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建练习记录失败: {str(e)}")


@router.put("/log/{id}/status", response_model=MessageResponse)
def update_exercise_log_status_api(
    id: int,
    status: int,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    更新练习记录状态

    ## 功能描述
    更新指定的练习记录信息状态。

    ## 请求参数
    - **id** (int): 练习情况ID，路径参数
    - **status** (int): 练习状态（0：待练习；1：练习中；2：已提交）

    ## 响应
    - **200**: 更新成功
        - 返回类型: MessageResponse
        - 包含更新成功的消息

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能更新属于当前用户的练习记录

    ## 错误处理
    - **400**: 请求参数错误，练习记录不存在、无权限访问或没有相关问题
    - **401**: 未授权访问，当令牌无效或过期时返回此错误
    - **404**: 练习记录不存在或无权限访问
    - **500**: 服务器内部错误
    """
    try:
        # 验证操作合法性
        if not validate_exercise_log_update(db, id, current_user):
            raise HTTPException(
                status_code=400,
                detail="练习记录不存在、无权限访问或没有相关问题",
            )

        success = update_exercise_log_status(db, id, status, current_user)
        if not success:
            raise HTTPException(status_code=404, detail="练习记录不存在或无权限访问")

        return MessageResponse(message="练习记录更新成功")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新练习记录失败: {str(e)}")
