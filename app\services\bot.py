from typing import Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.models.models import SysBconf, SysBot, TntBconf


def get_bot_config(db: Session, tenant_id: int, key: str) -> Optional[dict]:
    """
    获取机器人配置信息

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        key: 配置key

    Returns:
        包含机器人配置的字典或None
    """
    # 优先从租户配置中获取
    tnt_config = (
        db.query(TntBconf)
        .filter(
            and_(
                TntBconf.tenant_id == tenant_id,
                TntBconf.key == key,
            )
        )
        .first()
    )

    if tnt_config:
        bid = tnt_config.bid
    else:
        # 从全局配置中获取
        sys_config = db.query(SysBconf).filter(SysBconf.key == key).first()
        if not sys_config:
            return None
        bid = sys_config.bid

    # 获取机器人信息
    bot = db.query(SysBot).filter(SysBot.id == bid).first()
    if not bot:
        return None

    return {
        "api_endpoint": bot.api_endpoint,
        "api_key": bot.api_key,
    }
