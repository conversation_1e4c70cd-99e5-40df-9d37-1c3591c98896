from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.models.models import TntClass, TntClassStudent, TntClassExercise, TntExercise, TntTeacher, TntExerciseLog, TntWorksheet, TntScene
from app.schemas.clazz import ClassExerciseResponse
from app.utils.oss import get_oss_url


def get_student_classes(db: Session, student_id: int, tenant_id: int) -> List[TntClass]:
    """
    获取学员所在的所有班级
    
    Args:
        db: 数据库会话
        student_id: 学员ID
        tenant_id: 租户ID
        
    Returns:
        学员所在的班级列表，按priority从小到大排序，如果priority相同按开始时间倒序排列
    """
    # 查询学员所在的所有班级，通过班级学员关系表
    classes = (
        db.query(TntClass)
        .join(TntClassStudent, TntClassStudent.cid == TntClass.id)
        .filter(
            TntClassStudent.sid == student_id,
            TntClassStudent.tenant_id == tenant_id,
            TntClassStudent.active == 1,
            TntClass.active == 1,
            TntClass.tenant_id == tenant_id
        )
        .order_by(TntClassStudent.priority, desc(TntClass.btime))
        .all()
    )
    
    # 处理图片URL，生成签名URL
    for class_ in classes:
        if class_.pic:
            class_.pic = get_oss_url(class_.pic)
    
    return classes


def get_class_name(db: Session, class_id: int, tenant_id: int) -> str:
    """
    获取班级名称
    
    Args:
        db: 数据库会话
        class_id: 班级ID
        tenant_id: 租户ID
        
    Returns:
        班级名称
    """
    class_obj = (
        db.query(TntClass.name)
        .filter(
            and_(
                TntClass.id == class_id,
                TntClass.tenant_id == tenant_id,
                TntClass.active == 1
            )
        )
        .first()
    )
    return class_obj.name if class_obj else ""


def get_class_exercises(db: Session, class_id: int, tenant_id: int, student_id: int) -> List[ClassExerciseResponse]:
    """
    获取指定班级的所有练习信息（针对大数据量优化）
    
    优化策略：
    1. 使用子查询预过滤数据，减少大表连接开销
    2. 分步查询，先获取班级练习列表，再关联其他表
    3. 只选择必要字段，减少数据传输量
    
    建议索引：
    - TntClassExercise: (cid, tenant_id, active, eid)
    - TntExerciseLog: (cid, sid, tenant_id, active, eid)
    - TntExercise: (id, tenant_id, active)
    - TntTeacher: (id)
    
    Args:
        db: 数据库会话
        class_id: 班级ID
        tenant_id: 租户ID
        student_id: 学员ID（必需，用于获取练习状态）
        
    Returns:
        班级练习列表，按priority从小到大排序，priority一样的按创建时间倒序排列
    """
    # 步骤1：先获取该班级的练习ID列表（使用索引优化）
    class_exercise_subquery = (
        db.query(
            TntClassExercise.eid,
            TntClassExercise.tid,
            TntClassExercise.depend,
            TntClassExercise.priority
        )
        .filter(
            and_(
                TntClassExercise.cid == class_id,
                TntClassExercise.tenant_id == tenant_id,
                TntClassExercise.active == 1
            )
        )
        .subquery()
    )
    
    # 步骤2：预过滤练习日志（减少大表连接开销）
    exercise_log_subquery = (
        db.query(
            TntExerciseLog.eid,
            TntExerciseLog.id.label('el_id'),
            TntExerciseLog.status,
            TntExerciseLog.btime,
            TntExerciseLog.stime,
            TntExerciseLog.utime
        )
        .filter(
            and_(
                TntExerciseLog.cid == class_id,
                TntExerciseLog.sid == student_id,
                TntExerciseLog.tenant_id == tenant_id,
                TntExerciseLog.active == 1
            )
        )
        .subquery()
    )
    
    # 步骤3：预过滤作业单表（减少大表连接开销）
    worksheet_subquery = (
        db.query(
            TntWorksheet.eid,
            TntWorksheet.id.label('w_id')
        )
        .filter(
            TntWorksheet.tenant_id == tenant_id
        )
        .subquery()
    )
    
    # 步骤4：预过滤场景表（减少大表连接开销）
    scene_subquery = (
        db.query(
            TntScene.eid,
            TntScene.id.label('s_id')
        )
        .filter(
            TntScene.tenant_id == tenant_id
        )
        .subquery()
    )
    
    # 步骤5：构建主查询，只连接必要的表和数据
    exercises_query = (
        db.query(
            TntExercise.id.label('e_id'),
            TntExercise.title.label('e_title'),
            TntExercise.type.label('e_type'),
            TntExercise.pic.label('e_pic'),
            TntExercise.intro.label('e_intro'),
            TntExercise.duration.label('e_duration'),
            TntTeacher.id.label('t_id'),
            TntTeacher.name.label('t_name'),
            TntTeacher.avatar.label('t_avatar'),
            TntTeacher.intro.label('t_intro'),
            class_exercise_subquery.c.depend,
            worksheet_subquery.c.w_id,
            scene_subquery.c.s_id,
            class_exercise_subquery.c.priority,
            TntExercise.ctime,
            exercise_log_subquery.c.el_id,
            exercise_log_subquery.c.status.label('el_status'),
            exercise_log_subquery.c.btime.label('el_btime'),
            exercise_log_subquery.c.stime.label('el_stime'),
            exercise_log_subquery.c.utime.label('el_utime')
        )
        .join(class_exercise_subquery, class_exercise_subquery.c.eid == TntExercise.id)
        .outerjoin(TntTeacher, TntTeacher.id == class_exercise_subquery.c.tid)
        .outerjoin(
            exercise_log_subquery,
            exercise_log_subquery.c.eid == TntExercise.id
        )
        .outerjoin(
            worksheet_subquery,
            worksheet_subquery.c.eid == TntExercise.id
        )
        .outerjoin(
            scene_subquery,
            scene_subquery.c.eid == TntExercise.id
        )
        .filter(
            and_(
                TntExercise.active == 1,
                TntExercise.tenant_id == tenant_id
            )
        )
        .order_by(class_exercise_subquery.c.priority, desc(TntExercise.ctime))
        .all()
    )
    
    # 转换为 Pydantic 模型列表并处理 OSS URL
    exercises = []
    for row in exercises_query:
        exercise = ClassExerciseResponse(
            e_id=row.e_id,
            e_title=row.e_title,
            e_type=row.e_type,
            e_pic=get_oss_url(row.e_pic) if row.e_pic else None,
            e_intro=row.e_intro,
            e_duration=row.e_duration,
            t_id=row.t_id,
            t_name=row.t_name,
            t_avatar=get_oss_url(row.t_avatar) if row.t_avatar else None,
            t_intro=row.t_intro,
            depend=row.depend,
            w_id=row.w_id,
            s_id=row.s_id,
            el_id=row.el_id,
            el_status=row.el_status,
            el_btime=row.el_btime,
            el_stime=row.el_stime,
            el_utime=row.el_utime
        )
        exercises.append(exercise)
    
    return exercises