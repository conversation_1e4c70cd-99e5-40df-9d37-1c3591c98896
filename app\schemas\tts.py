from pydantic import BaseModel, Field
from typing import Optional


class TTSWebSocketRequest(BaseModel):
    """TTS WebSocket请求模型"""
    text: str = Field(..., description="要合成的文本内容", min_length=1, max_length=5000)
    timbre: str = Field(..., description="音色类型", min_length=1)
    speed_ratio: Optional[float] = Field(1.0, description="语速比例", ge=0.5, le=2.0)
    volume_ratio: Optional[float] = Field(1.0, description="音量比例", ge=0.5, le=2.0)
    pitch_ratio: Optional[float] = Field(1.0, description="音调比例", ge=0.5, le=2.0)
    encoding: Optional[str] = Field("mp3", description="音频编码格式")


class TTSWebSocketResponse(BaseModel):
    """TTS WebSocket响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    request_id: Optional[str] = Field(None, description="请求ID")


class TTSErrorResponse(BaseModel):
    """TTS错误响应模型"""
    error_code: int = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    request_id: Optional[str] = Field(None, description="请求ID")
