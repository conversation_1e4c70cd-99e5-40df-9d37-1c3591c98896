# 删除整体评论接口使用说明

## 接口概述

删除整体评论接口用于删除指定练习情况的整体评论，将 `tnt_exercise_log` 表中的 `comments` 字段设置为 `null`。

## 接口信息

- **请求方法**: DELETE
- **请求路径**: `/api/v1/scene/comments`
- **内容类型**: application/json
- **认证方式**: Bearer Token

## 请求参数

### 请求体

```json
{
    "elid": 123
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| elid   | int  | 是   | 练习情况ID |

### 响应信息

- **成功响应**: 200 OK
- **内容类型**: application/json
- **响应格式**: 

```json
{
    "message": "整体评论删除成功"
}
```

### 权限要求

- 需要有效的用户身份令牌
- 只能删除当前租户下的练习情况评论
- 只能删除属于当前用户的练习情况评论

## 业务逻辑

### Step 1: 验证权限
- 验证练习情况是否存在
- 验证是否属于当前用户
- 验证是否属于当前租户
- 验证记录是否有效（active=1）

### Step 2: 删除评论
- 将 `tnt_exercise_log` 表中对应记录的 `comments` 字段设置为 `null`
- 提交数据库事务

## 错误处理

### 404 错误
- 练习情况不存在
- 无权访问该练习情况（不属于当前用户或租户）
- 练习情况记录无效（active=0）

### 500 错误
- 数据库操作失败
- 事务回滚失败

## 使用示例

### cURL 示例

```bash
curl -X DELETE "http://localhost:8000/api/v1/scene/comments" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "elid": 123
  }'
```

### Python 示例

```python
import requests

url = "http://localhost:8000/api/v1/scene/comments"
headers = {
    "Authorization": "Bearer YOUR_ACCESS_TOKEN",
    "Content-Type": "application/json"
}
data = {
    "elid": 123
}

response = requests.delete(url, headers=headers, json=data)

if response.status_code == 200:
    result = response.json()
    print(f"删除成功: {result['message']}")
else:
    print(f"删除失败: {response.status_code} - {response.text}")
```

### JavaScript 示例

```javascript
const deleteComments = async (elid, token) => {
    try {
        const response = await fetch('/api/v1/scene/comments', {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ elid })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('删除成功:', result.message);
            return true;
        } else {
            const error = await response.text();
            console.error('删除失败:', error);
            return false;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return false;
    }
};

// 使用示例
deleteComments(123, 'YOUR_ACCESS_TOKEN');
```

## 注意事项

1. **不可恢复**: 删除操作会立即生效且无法恢复，需要重新生成评论
2. **权限控制**: 严格的权限控制确保用户只能删除自己的数据
3. **事务安全**: 删除操作在数据库事务中进行，确保数据一致性
4. **幂等性**: 重复删除同一条评论不会产生错误

## 数据库影响

### 查询的表
- tnt_exercise_log: 验证练习情况并删除评论

### 更新的表
- tnt_exercise_log: 将 comments 字段设置为 null

## 相关接口

- [创建整体评论接口](API_COMMENTS_USAGE.md): POST /api/v1/scene/comments
- [获取场景基本信息接口](README.md): GET /api/v1/scene/{id}

## 错误码说明

| 状态码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | 整体评论删除成功 | 删除操作成功完成 |
| 404 | 练习情况不存在 | 指定的练习情况ID不存在或无权访问 |
| 500 | 删除整体评论失败 | 服务器内部错误，数据库操作失败 |
