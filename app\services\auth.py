from typing import Optional, <PERSON><PERSON>

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session, joinedload

from app.core.config import settings
from app.core.security import create_access_token, get_password_hash, verify_password
from app.db.session import get_db
from app.models.models import Sys<PERSON><PERSON>, TntStudent
from app.schemas.auth import PasswordUpdateRequest, UserTokenPayload

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


def get_user_by_username(db: Session, username: str) -> Optional[SysUser]:
    """通过用户名获取用户"""
    return db.query(SysUser).filter(SysUser.username == username).first()


def authenticate_user(db: Session, username: str, password: str) -> Optional[SysUser]:
    """认证用户"""
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.passwd):
        return None
    return user


def update_user_password(
    db: Session, user_id: int, old_password: str, new_password: str
) -> bool:
    """更新用户密码的通用函数"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not user:
        return False

    if not verify_password(old_password, user.passwd):
        return False

    user.passwd = get_password_hash(new_password)
    user.token_version += 1  # 使当前token失效
    db.commit()
    return True


def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> TntStudent:
    """获取当前用户"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = UserTokenPayload(**payload)
    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    member = db.query(TntStudent).filter(TntStudent.uid == token_data.uid).first()
    if not member:
        raise HTTPException(status_code=404, detail="Not found")
    if not member.user.active:
        raise HTTPException(status_code=400, detail="Inactive")

    return member


def login(
    db: Session, username: str, password: str
) -> Tuple[Optional[TntStudent], Optional[str], Optional[str]]:
    """用户登录

    Returns:
        Tuple[Optional[TntStudent], Optional[str], Optional[str]]: (member, token, error_message)
    """
    user = authenticate_user(db, username, password)
    if not user:
        return None, None, "用户名或密码错误"

    member = db.query(TntStudent).filter(TntStudent.uid == user.id).first()
    if not member:
        return None, None, "该用户不是有效成员"

    if not user.active:
        return None, None, "用户已被禁用"

    token = create_access_token(
        data={
            "uid": user.id,
            "utype": "tnt",
        }
    )

    return member, token, None


def logout(db: Session, user_id: int) -> None:
    """用户登出"""
    user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if user:
        user.token_version += 1
        db.commit()


def get_user_profile(db: Session, member_id: int) -> Optional[TntStudent]:
    """获取用户个人信息"""
    return (
        db.query(TntStudent)
        .options(joinedload(TntStudent.user))
        .filter(TntStudent.id == member_id)
        .first()
    )


def update_user_password_service(
    db: Session, member_id: int, password_update: PasswordUpdateRequest
) -> bool:
    """更新用户密码"""
    member = get_user_profile(db, member_id)
    if not member:
        return False

    return update_user_password(
        db, member.uid, password_update.old_password, password_update.new_password
    )
