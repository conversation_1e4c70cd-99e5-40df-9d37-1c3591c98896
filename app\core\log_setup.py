import logging
import sys
from logging.handlers import RotatingFile<PERSON>and<PERSON>, TimedRotatingFileHandler
from pathlib import Path

from app.core.config import settings


def setup_logging() -> logging.Logger:
    """设置日志配置"""
    try:
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 创建格式化器
        formatter = logging.Formatter(
            fmt=settings.LOG_FORMAT, datefmt=settings.LOG_DATE_FORMAT
        )

        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(settings.log_level)

        # 清除现有的处理器（避免重复）
        root_logger.handlers.clear()

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # 按大小轮转的文件处理器
        size_handler = RotatingFileHandler(
            log_dir / "app.log",
            maxBytes=settings.LOG_FILE_SIZE,
            backupCount=settings.LOG_BACKUP_COUNT,
            encoding="utf-8",
        )
        size_handler.setFormatter(formatter)
        root_logger.addHandler(size_handler)

        # 按时间轮转的文件处理器
        time_handler = TimedRotatingFileHandler(
            log_dir / "app.daily.log",
            when="midnight",
            interval=1,
            backupCount=settings.LOG_BACKUP_COUNT,
            encoding="utf-8",
        )
        time_handler.setFormatter(formatter)
        root_logger.addHandler(time_handler)

        # 设置第三方库的日志级别
        logging.getLogger("uvicorn").setLevel(logging.INFO)
        logging.getLogger("sqlalchemy").setLevel(logging.WARNING)

        return root_logger

    except Exception as e:
        # 确保logging初始化失败时不会导致应用崩溃
        fallback_logger = logging.getLogger()
        fallback_logger.handlers.clear()
        handler = logging.StreamHandler(sys.stderr)
        handler.setFormatter(logging.Formatter(settings.LOG_FORMAT))
        fallback_logger.addHandler(handler)
        fallback_logger.error(f"Failed to setup logging: {e}")
        return fallback_logger
