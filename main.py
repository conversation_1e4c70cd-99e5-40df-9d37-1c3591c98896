from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from app.api.v1 import api_router
from app.core.config import settings
from app.core.log_setup import setup_logging
from app.core.middleware import ExceptionMiddleware

# 定义API文档的基本信息
API_TITLE = settings.PROJECT_NAME + " API"
API_DESCRIPTION = settings.PROJECT_NAME + " Backend API Service"
API_VERSION = settings.VERSION


@asynccontextmanager
async def lifespan(_):
    # 这里的代码在应用启动时执行
    setup_logging()
    settings.start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    yield
    # 这里的代码在应用关闭时执行


app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=API_TITLE,
        version=API_VERSION,
        description=API_DESCRIPTION,
        routes=app.routes,
    )

    # 添加 JWT bearer 认证方案
    openapi_schema["components"]["securitySchemes"] = {
        "JWT": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT Bearer token",
        }
    }

    # 添加 WebSocket 端点的自定义文档
    if "paths" not in openapi_schema:
        openapi_schema["paths"] = {}

    # 添加 TTS WebSocket 端点文档
    openapi_schema["paths"]["/api/v1/tts/ws"] = {
        "get": {
            "tags": ["tts"],
            "summary": "Tts WebSocket Endpoint",
            "description": """
## 功能描述
提供流式语音合成服务，客户端通过WebSocket连接发送文本，服务器返回合成的音频流。

## 连接信息
- **协议**: WebSocket
- **URL**: `ws://localhost:8000/api/v1/tts/ws`
- **认证要求**: 必须提供有效的JWT token

## 认证方式
支持两种认证方式：
1. **查询参数**: `ws://localhost:8000/api/v1/tts/ws?token=YOUR_JWT_TOKEN`
2. **Authorization header**: `Authorization: Bearer YOUR_JWT_TOKEN`

## 连接流程
1. 客户端建立WebSocket连接（需要提供有效token）
2. 服务器验证用户身份
3. 发送JSON格式的TTS请求
4. 服务器返回确认响应
5. 服务器流式返回音频数据
6. 服务器发送完成响应
7. 连接结束

## 请求格式
客户端需要发送JSON格式的请求：
```json
{
    "text": "要合成的文本内容",
    "timbre": "音色类型",
    "speed_ratio": 1.0,
    "volume_ratio": 1.0,
    "pitch_ratio": 1.0,
    "encoding": "mp3"
}
```

## 响应格式
### 1. 确认响应（JSON）
```json
{
    "success": true,
    "message": "Request received, starting TTS processing",
    "request_id": null
}
```

### 2. 音频数据流（二进制）
服务器会流式发送音频二进制数据块

### 3. 完成响应（JSON）
```json
{
    "success": true,
    "message": "TTS processing completed",
    "request_id": null
}
```

### 4. 错误响应（JSON）
```json
{
    "error_code": 400,
    "error_message": "错误描述",
    "request_id": null
}
```

## 错误处理
- **认证失败**: 拒绝连接（code: 1008）
- **连接断开**: 自动清理资源
- **参数错误**: 返回错误信息并关闭连接（code: 1003）
- **TTS服务错误**: 返回错误信息并关闭连接（code: 1011）

## 使用示例
```javascript
// 方式1: 通过查询参数传递token
const token = 'YOUR_JWT_TOKEN';
const ws = new WebSocket(`ws://localhost:8000/api/v1/tts/ws?token=${token}`);

// 方式2: 通过Authorization header（需要支持的WebSocket客户端）
const ws = new WebSocket('ws://localhost:8000/api/v1/tts/ws', [], {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});

ws.onopen = function() {
    // 发送TTS请求
    ws.send(JSON.stringify({
        text: "你好，这是一个测试",
        timbre: "zh_female_shuangkuaisisi_moon_bigtts",
        speed_ratio: 1.0,
        volume_ratio: 1.0,
        pitch_ratio: 1.0,
        encoding: "mp3"
    }));
};

ws.onmessage = function(event) {
    if (event.data instanceof Blob) {
        // 音频数据
        console.log('Received audio data:', event.data);
    } else {
        // JSON响应
        const response = JSON.parse(event.data);
        console.log('Received response:', response);
    }
};

ws.onclose = function(event) {
    if (event.code === 1008) {
        console.error('认证失败，请检查token是否有效');
    }
};
```
            """,
            "parameters": [
                {
                    "name": "token",
                    "in": "query",
                    "required": False,
                    "schema": {"type": "string"},
                    "description": "JWT认证token（可选，也可通过Authorization header传递）"
                },
                {
                    "name": "Authorization",
                    "in": "header",
                    "required": False,
                    "schema": {"type": "string"},
                    "description": "Bearer JWT token（格式：Bearer YOUR_JWT_TOKEN）"
                },
                {
                    "name": "Upgrade",
                    "in": "header",
                    "required": True,
                    "schema": {"type": "string", "enum": ["websocket"]},
                    "description": "必须设置为 'websocket'"
                },
                {
                    "name": "Connection",
                    "in": "header",
                    "required": True,
                    "schema": {"type": "string", "enum": ["Upgrade"]},
                    "description": "必须设置为 'Upgrade'"
                },
                {
                    "name": "Sec-WebSocket-Key",
                    "in": "header",
                    "required": True,
                    "schema": {"type": "string"},
                    "description": "WebSocket握手密钥"
                },
                {
                    "name": "Sec-WebSocket-Version",
                    "in": "header",
                    "required": True,
                    "schema": {"type": "string", "enum": ["13"]},
                    "description": "WebSocket版本，必须为13"
                }
            ],
            "responses": {
                "101": {
                    "description": "WebSocket连接建立成功",
                    "headers": {
                        "Upgrade": {
                            "schema": {"type": "string"},
                            "description": "websocket"
                        },
                        "Connection": {
                            "schema": {"type": "string"},
                            "description": "Upgrade"
                        },
                        "Sec-WebSocket-Accept": {
                            "schema": {"type": "string"},
                            "description": "WebSocket接受密钥"
                        }
                    }
                },
                "400": {
                    "description": "WebSocket握手失败或请求参数错误",
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/TTSErrorResponse"
                            }
                        }
                    }
                },
                "401": {
                    "description": "认证失败 - 未提供token或token无效",
                    "content": {
                        "text/plain": {
                            "schema": {
                                "type": "string",
                                "example": "Authentication failed"
                            }
                        }
                    }
                },
                "500": {
                    "description": "服务器内部错误",
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/TTSErrorResponse"
                            }
                        }
                    }
                }
            },
            "security": [
                {
                    "JWT": []
                }
            ]
        }
    }

    # 确保 components/schemas 存在
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    if "schemas" not in openapi_schema["components"]:
        openapi_schema["components"]["schemas"] = {}

    # 添加 TTS 相关的 schema 定义
    openapi_schema["components"]["schemas"].update({
        "TTSWebSocketRequest": {
            "type": "object",
            "required": ["text", "timbre"],
            "properties": {
                "text": {
                    "type": "string",
                    "description": "要合成的文本内容",
                    "minLength": 1,
                    "maxLength": 5000,
                    "example": "你好，这是一个测试"
                },
                "timbre": {
                    "type": "string",
                    "description": "音色类型",
                    "minLength": 1,
                    "example": "zh_female_shuangkuaisisi_moon_bigtts"
                },
                "speed_ratio": {
                    "type": "number",
                    "description": "语速比例",
                    "minimum": 0.5,
                    "maximum": 2.0,
                    "default": 1.0,
                    "example": 1.0
                },
                "volume_ratio": {
                    "type": "number",
                    "description": "音量比例",
                    "minimum": 0.5,
                    "maximum": 2.0,
                    "default": 1.0,
                    "example": 1.0
                },
                "pitch_ratio": {
                    "type": "number",
                    "description": "音调比例",
                    "minimum": 0.5,
                    "maximum": 2.0,
                    "default": 1.0,
                    "example": 1.0
                },
                "encoding": {
                    "type": "string",
                    "description": "音频编码格式",
                    "default": "mp3",
                    "example": "mp3"
                }
            },
            "example": {
                "text": "你好，这是一个测试",
                "timbre": "zh_female_shuangkuaisisi_moon_bigtts",
                "speed_ratio": 1.0,
                "volume_ratio": 1.0,
                "pitch_ratio": 1.0,
                "encoding": "mp3"
            }
        },
        "TTSWebSocketResponse": {
            "type": "object",
            "required": ["success", "message"],
            "properties": {
                "success": {
                    "type": "boolean",
                    "description": "请求是否成功"
                },
                "message": {
                    "type": "string",
                    "description": "响应消息"
                },
                "request_id": {
                    "type": "string",
                    "description": "请求ID",
                    "nullable": True
                }
            },
            "example": {
                "success": True,
                "message": "Request received, starting TTS processing",
                "request_id": None
            }
        },
        "TTSErrorResponse": {
            "type": "object",
            "required": ["error_code", "error_message"],
            "properties": {
                "error_code": {
                    "type": "integer",
                    "description": "错误代码"
                },
                "error_message": {
                    "type": "string",
                    "description": "错误消息"
                },
                "request_id": {
                    "type": "string",
                    "description": "请求ID",
                    "nullable": True
                }
            },
            "example": {
                "error_code": 400,
                "error_message": "Invalid request format",
                "request_id": None
            }
        }
    })

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

# 定义允许的源（开发环境用）
origins = [
    "http://localhost：5173",
]

app.add_middleware(
    CORSMiddleware,  # Type:ignore
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

app.add_middleware(ExceptionMiddleware)  # Type:ignore

app.include_router(api_router, prefix="/api/v1")
